# Payroll MCP Python

A comprehensive Model Context Protocol (MCP) server implementation using Python's FastMCP library for Payroll Grid API integration. This server provides a single `payroll` tool that interfaces with external payroll grid APIs via JSON-RPC 2.0.

## Features

- 🚀 **FastMCP Integration**: Built on the modern FastMCP framework with SSE transport
- 🔧 **Single Tool Interface**: Provides exactly one `payroll` tool as specified
- 📊 **Comprehensive Parameter Support**: Supports all parameters from payroll-grid-api-parameters.md
- 🔒 **Secure Authentication**: Token-based authentication with the external API
- 📝 **Structured Logging**: JSON and text logging formats with multiple levels
- 🐳 **Production Ready**: Docker containerization with security best practices
- ⚡ **Async Performance**: Fully asynchronous implementation with proper error handling
- 🔄 **Retry Logic**: Configurable HTTP retry mechanisms with exponential backoff

## Quick Start

### Prerequisites

- Python 3.11 or higher
- [uv](https://github.com/astral-sh/uv) package manager
- Docker and Docker Compose (for containerized deployment)

### Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd payroll-mcp-python
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your actual configuration
   ```

3. **Install dependencies:**
   ```bash
   uv pip install -e .
   ```

4. **Run the server:**
   ```bash
   python -m payroll_mcp.server
   ```

### Docker Deployment

1. **Build and run with Docker Compose:**
   ```bash
   docker-compose up --build
   ```

2. **For development:**
   ```bash
   docker-compose --profile dev up --build
   ```

## Configuration

### Required Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `PAYROLL_API_URL` | Base URL for the payroll API | `https://api.payroll-system.com` |
| `PAYROLL_ACCESS_TOKEN` | Access token for API authentication | `your-secret-token` |

### Optional Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `MCP_HOST` | `0.0.0.0` | Host to bind MCP server |
| `MCP_PORT` | `8000` | Port for MCP server |
| `LOG_LEVEL` | `INFO` | Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL) |
| `LOG_FORMAT` | `json` | Log format (json, text) |
| `HTTP_TIMEOUT` | `30` | HTTP request timeout in seconds |
| `HTTP_MAX_RETRIES` | `3` | Maximum HTTP retry attempts |

## API Integration

### External API Endpoint

The server constructs the full endpoint URL by appending `index.php?payroll-rpc=payroll-grid` to the base URL:

```
{PAYROLL_API_URL}/index.php?payroll-rpc=payroll-grid
```

### JSON-RPC 2.0 Format

The server sends requests in this exact format:

```json
{
    "method": "read",
    "params": [
        {
            // Parameters from the payroll tool
        },
        1,              // page
        30,             // rows
        "owner_names",  // sort
        "asc"          // order
    ],
    "id": 1,
    "jsonrpc": "2.0"
}
```

## Tool Usage

### Payroll Tool

The `payroll` tool accepts comprehensive parameters for querying payroll data:

#### Required Parameters
- `type`: Operation mode (`"owners"`, `"sums"`, `"payroll_by_owner"`)
- `farming_year`: Farming year ID (integer)

#### Optional Parameters
- **Date Range**: `payroll_from_date`, `payroll_to_date`
- **Location Filters**: `payroll_ekate`, `payroll_farming`, `rent_place`
- **Owner Filters**: `owner_type`, `owner_names`, `egn`, `eik`, `company_name`
- **Advanced Filters**: `owner_egns`, `company_eiks`
- **Representative Filters**: `rep_names`, `rep_egn`, `rep_rent_place`
- **Heritor Filters**: `heritor_names`, `heritor_egn`
- **Pagination**: `page`, `rows`, `sort`, `order`

#### Example Usage

```python
# Example MCP client call
result = await client.call_tool("payroll", {
    "type": "owners",
    "farming_year": 16,
    "payroll_from_date": "2024-10-01",
    "payroll_to_date": "2025-09-30",
    "owner_type": "1",
    "page": 1,
    "rows": 30
})
```

## Development

### Project Structure

```
payroll-mcp-python/
├── src/payroll_mcp/
│   ├── __init__.py          # Package initialization
│   ├── server.py            # Main MCP server
│   ├── models.py            # Pydantic models
│   ├── client.py            # JSON-RPC HTTP client
│   ├── config.py            # Configuration management
│   └── logging.py           # Logging setup
├── pyproject.toml           # Project configuration
├── Dockerfile               # Production container
├── docker-compose.yml       # Container orchestration
├── .env.example             # Environment template
└── README.md               # This file
```

### Running Tests

```bash
# Install development dependencies
uv pip install -e ".[dev]"

# Run tests
pytest

# Run with coverage
pytest --cov=payroll_mcp
```

### Code Quality

```bash
# Format code
black src/ tests/

# Sort imports
isort src/ tests/

# Lint code
flake8 src/ tests/

# Type checking
mypy src/
```

## Monitoring and Logging

### Structured Logging

The server uses structured logging with configurable formats:

- **JSON Format** (production): Machine-readable structured logs
- **Text Format** (development): Human-readable colored output

### Log Levels

- `DEBUG`: Detailed debugging information
- `INFO`: General operational messages
- `WARNING`: Warning messages for potential issues
- `ERROR`: Error messages for failures
- `CRITICAL`: Critical errors requiring immediate attention

### Health Checks

The Docker container includes health checks accessible at:
```
GET http://localhost:8000/health
```

## Security

### Best Practices Implemented

- 🔒 Non-root user in Docker container
- 🛡️ Read-only root filesystem
- 🚫 No new privileges security option
- 🔑 Token-based authentication
- 📝 Comprehensive input validation
- 🔍 Structured audit logging

### Security Considerations

- Keep access tokens secure and rotate regularly
- Use HTTPS for all external API communications
- Monitor logs for suspicious activity
- Implement network security policies
- Regular security updates for dependencies

## Troubleshooting

### Common Issues

1. **Configuration Errors**: Check environment variables in `.env`
2. **API Connection Issues**: Verify `PAYROLL_API_URL` and network connectivity
3. **Authentication Failures**: Validate `PAYROLL_ACCESS_TOKEN`
4. **Port Conflicts**: Ensure `MCP_PORT` is available

### Debug Mode

Enable debug logging for troubleshooting:

```bash
export LOG_LEVEL=DEBUG
export LOG_FORMAT=text
python -m payroll_mcp.server
```

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
- Check the logs for error details
- Verify configuration against `.env.example`
- Review the parameter documentation
- Open an issue in the repository
