services:
  payroll-mcp-server:
    image: technofarm/payroll-mcp-server:${IMAGE_TAG_NAME}
    container_name: ${CONTAINER_NAME}
    environment:
      # Override defaults if needed
      - MCP_HOST=0.0.0.0
      - MCP_PORT=${MCP_PORT:-8000}
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
      - HTTP_TIMEOUT=30
      - HTTP_MAX_RETRIES=3
      - PAYROLL_API_URL
      - PAYROLL_ACCESS_TOKEN
    ports:
      - "${MCP_PORT:-8000}:8000"
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/sse"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    # Security options
    security_opt:
      - no-new-privileges:true
    
    # Read-only root filesystem for security
    read_only: true
    
    # Temporary filesystems for writable directories
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
      - /app/logs:noexec,nosuid,size=50m
    
    # Networks
    networks:
      - payroll-mcp-network
    # Restart policy
    restart: unless-stopped

networks:
  payroll-mcp-network:
    external: true

# Volumes for persistent data (if needed in the future)
volumes:
  payroll-mcp-logs:
    name: ${CONTAINER_NAME}-logs
