"""
Tests for configuration management.
"""

import os
import pytest
from unittest.mock import patch

from payroll_mcp.config import Config, ConfigurationError, load_config


class TestConfig:
    """Test configuration loading and validation."""
    
    def test_config_validation_success(self):
        """Test successful configuration validation."""
        config = Config(
            payroll_api_url="https://api.example.com",
            payroll_access_token="test-token-1234567890"
        )
        
        assert config.payroll_api_url == "https://api.example.com"
        assert config.payroll_access_token == "test-token-1234567890"
        assert config.mcp_host == "0.0.0.0"
        assert config.mcp_port == 8000
        assert config.log_level == "INFO"
        assert config.log_format == "json"
    
    def test_payroll_endpoint_url_property(self):
        """Test payroll endpoint URL construction."""
        config = Config(
            payroll_api_url="https://api.example.com",
            payroll_access_token="test-token-1234567890"
        )
        
        expected_url = "https://api.example.com/index.php?payroll-rpc=payroll-grid"
        assert config.payroll_endpoint_url == expected_url
    
    def test_invalid_payroll_api_url(self):
        """Test validation of invalid payroll API URL."""
        with pytest.raises(ConfigurationError, match="PAYROLL_API_URL must be a valid URL"):
            Config(
                payroll_api_url="not-a-url",
                payroll_access_token="test-token-1234567890"
            )
    
    def test_missing_payroll_api_url(self):
        """Test validation of missing payroll API URL."""
        with pytest.raises(ConfigurationError, match="PAYROLL_API_URL is required"):
            Config(
                payroll_api_url="",
                payroll_access_token="test-token-1234567890"
            )
    
    def test_short_access_token(self):
        """Test validation of short access token."""
        with pytest.raises(ConfigurationError, match="PAYROLL_ACCESS_TOKEN appears to be too short"):
            Config(
                payroll_api_url="https://api.example.com",
                payroll_access_token="short"
            )
    
    def test_invalid_port(self):
        """Test validation of invalid port."""
        with pytest.raises(ConfigurationError, match="MCP_PORT must be between 1 and 65535"):
            Config(
                payroll_api_url="https://api.example.com",
                payroll_access_token="test-token-1234567890",
                mcp_port=70000
            )
    
    def test_invalid_log_level(self):
        """Test validation of invalid log level."""
        with pytest.raises(ConfigurationError, match="LOG_LEVEL must be one of"):
            Config(
                payroll_api_url="https://api.example.com",
                payroll_access_token="test-token-1234567890",
                log_level="INVALID"
            )
    
    def test_is_development_property(self):
        """Test is_development property."""
        config_debug = Config(
            payroll_api_url="https://api.example.com",
            payroll_access_token="test-token-1234567890",
            log_level="DEBUG"
        )
        assert config_debug.is_development is True
        
        config_info = Config(
            payroll_api_url="https://api.example.com",
            payroll_access_token="test-token-1234567890",
            log_level="INFO"
        )
        assert config_info.is_development is False


class TestLoadConfig:
    """Test configuration loading from environment."""
    
    @patch.dict(os.environ, {
        'PAYROLL_API_URL': 'https://test.example.com',
        'PAYROLL_ACCESS_TOKEN': 'test-token-1234567890',
        'MCP_PORT': '9000',
        'LOG_LEVEL': 'DEBUG'
    })
    def test_load_config_from_env(self):
        """Test loading configuration from environment variables."""
        config = load_config()
        
        assert config.payroll_api_url == "https://test.example.com"
        assert config.payroll_access_token == "test-token-1234567890"
        assert config.mcp_port == 9000
        assert config.log_level == "DEBUG"
    
    @patch.dict(os.environ, {
        'PAYROLL_API_URL': '',
        'PAYROLL_ACCESS_TOKEN': 'test-token-1234567890'
    })
    def test_load_config_missing_required(self):
        """Test loading configuration with missing required values."""
        with pytest.raises(ConfigurationError):
            load_config()
    
    @patch.dict(os.environ, {
        'PAYROLL_API_URL': 'https://test.example.com',
        'PAYROLL_ACCESS_TOKEN': 'test-token-1234567890',
        'MCP_PORT': 'not-a-number'
    })
    def test_load_config_invalid_type(self):
        """Test loading configuration with invalid type conversion."""
        with pytest.raises(ConfigurationError):
            load_config()
