"""
Functional tests for the payroll system with default parameters.
"""

import pytest
from payroll_mcp.models import PayrollParameters


class TestFunctionalDefaults:
    """Test functional behavior with default parameters."""
    
    def test_payroll_parameters_creation_with_defaults(self):
        """Test that PayrollParameters can be created with all defaults."""
        # This should work without any parameters
        params = PayrollParameters()
        
        # Verify all defaults are set correctly
        assert params.type == "owners"
        assert params.farming_year == 16
        assert params.payroll_from_date == "2024-10-01"
        assert params.payroll_to_date == "2025-09-30"
        assert params.payroll_ekate == [""]
        assert params.payroll_farming == [""]
        assert params.owner_type == "0,1"
        assert params.owner_names == ""
        assert params.egn == ""
        assert params.eik == ""
        assert params.company_name == ""
        assert params.owner_egns == []
        assert params.company_eiks == []
        assert params.rep_names == ""
        assert params.rep_egn == ""
        assert params.rep_rent_place == ""
        assert params.rent_place == ""
        assert params.heritor_names == ""
        assert params.heritor_egn == ""
    
    def test_api_params_with_defaults(self):
        """Test that API parameters include all default values."""
        params = PayrollParameters()
        api_params = params.to_api_params()
        
        # Verify the expected structure matches the default values from the requirements
        expected_defaults = {
            "type": "owners",
            "payroll_from_date": "2024-10-01",
            "payroll_to_date": "2025-09-30",
            "farming_year": 16,
            "payroll_ekate": [""],
            "payroll_farming": [""],
            "owner_type": "0,1",
            "owner_names": "",
            "egn": "",
            "eik": "",
            "company_name": "",
            "rep_names": "",
            "rep_egn": "",
            "heritor_names": "",
            "heritor_egn": "",
            "rep_rent_place": "",
            "rent_place": "",
            "owner_egns": [],
            "company_eiks": []
        }
        
        # Check that all expected defaults are present
        for key, expected_value in expected_defaults.items():
            assert key in api_params, f"Missing key: {key}"
            assert api_params[key] == expected_value, f"Wrong value for {key}: expected {expected_value}, got {api_params[key]}"
    
    def test_partial_parameter_override(self):
        """Test that partial parameter override works correctly."""
        params = PayrollParameters(
            owner_names="John Doe",
            egn="1234567890",
            payroll_from_date="2023-01-01"
        )
        
        api_params = params.to_api_params()
        
        # Overridden values should be present
        assert api_params["owner_names"] == "John Doe"
        assert api_params["egn"] == "1234567890"
        assert api_params["payroll_from_date"] == "2023-01-01"
        
        # Default values should still be present for other fields
        assert api_params["type"] == "owners"
        assert api_params["farming_year"] == 16
        assert api_params["payroll_to_date"] == "2025-09-30"
        assert api_params["owner_type"] == "0,1"
        assert api_params["company_name"] == ""
        assert api_params["payroll_ekate"] == [""]
    
    def test_list_parameter_override(self):
        """Test that list parameters can be overridden correctly."""
        params = PayrollParameters(
            payroll_ekate=["EKATE1", "EKATE2"],
            owner_egns=["1111111111", "*********2"],
            payroll_farming=["FARM1"]
        )
        
        api_params = params.to_api_params()
        
        # Overridden list values
        assert api_params["payroll_ekate"] == ["EKATE1", "EKATE2"]
        assert api_params["owner_egns"] == ["1111111111", "*********2"]
        assert api_params["payroll_farming"] == ["FARM1"]
        
        # Default list values for other fields
        assert api_params["company_eiks"] == []
        
        # Other defaults should remain
        assert api_params["type"] == "owners"
        assert api_params["farming_year"] == 16
        assert api_params["owner_names"] == ""
    
    def test_complete_parameter_override(self):
        """Test that all parameters can be overridden."""
        params = PayrollParameters(
            type="sums",
            farming_year=17,
            payroll_from_date="2023-01-01",
            payroll_to_date="2023-12-31",
            payroll_ekate=["CUSTOM_EKATE"],
            payroll_farming=["CUSTOM_FARM"],
            owner_type="1",
            owner_names="Custom Owner",
            egn="9999999999",
            eik="*********",
            company_name="Custom Company",
            owner_egns=["1111111111"],
            company_eiks=["*********"],
            rep_names="Custom Rep",
            rep_egn="7777777777",
            rep_rent_place="Custom Rep Place",
            rent_place="Custom Place",
            heritor_names="Custom Heritor",
            heritor_egn="6666666666"
        )
        
        api_params = params.to_api_params()
        
        # All values should be overridden
        assert api_params["type"] == "sums"
        assert api_params["farming_year"] == 17
        assert api_params["payroll_from_date"] == "2023-01-01"
        assert api_params["payroll_to_date"] == "2023-12-31"
        assert api_params["payroll_ekate"] == ["CUSTOM_EKATE"]
        assert api_params["payroll_farming"] == ["CUSTOM_FARM"]
        assert api_params["owner_type"] == "1"
        assert api_params["owner_names"] == "Custom Owner"
        assert api_params["egn"] == "9999999999"
        assert api_params["eik"] == "*********"
        assert api_params["company_name"] == "Custom Company"
        assert api_params["owner_egns"] == ["1111111111"]
        assert api_params["company_eiks"] == ["*********"]
        assert api_params["rep_names"] == "Custom Rep"
        assert api_params["rep_egn"] == "7777777777"
        assert api_params["rep_rent_place"] == "Custom Rep Place"
        assert api_params["rent_place"] == "Custom Place"
        assert api_params["heritor_names"] == "Custom Heritor"
        assert api_params["heritor_egn"] == "6666666666"
    
    def test_api_params_always_complete(self):
        """Test that API params always include all required fields."""
        # Test with minimal parameters
        params = PayrollParameters(owner_names="Test")
        api_params = params.to_api_params()
        
        # Should have all expected fields
        expected_fields = {
            "type", "farming_year", "payroll_from_date", "payroll_to_date",
            "payroll_ekate", "payroll_farming", "owner_type", "owner_names",
            "egn", "eik", "company_name", "owner_egns", "company_eiks",
            "rep_names", "rep_egn", "rep_rent_place", "rent_place",
            "heritor_names", "heritor_egn"
        }
        
        assert set(api_params.keys()) == expected_fields
        
        # No field should be None
        for key, value in api_params.items():
            assert value is not None, f"Field {key} should not be None"
    
    def test_default_values_match_requirements(self):
        """Test that default values match the exact requirements."""
        params = PayrollParameters()
        api_params = params.to_api_params()
        
        # Test against the exact default values specified in the requirements
        required_defaults = {
            "type": "owners",
            "payroll_from_date": "2024-10-01",
            "payroll_to_date": "2025-09-30",
            "farming_year": 16,
            "payroll_ekate": [""],
            "payroll_farming": [""],
            "owner_type": "0,1",
            "owner_names": "",
            "egn": "",
            "eik": "",
            "company_name": "",
            "rep_names": "",
            "rep_egn": "",
            "heritor_names": "",
            "heritor_egn": "",
            "rep_rent_place": "",
            "rent_place": "",
            "owner_egns": [],
            "company_eiks": []
        }
        
        for key, expected_value in required_defaults.items():
            actual_value = api_params[key]
            assert actual_value == expected_value, (
                f"Default value mismatch for '{key}': "
                f"expected {expected_value!r}, got {actual_value!r}"
            )
