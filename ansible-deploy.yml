---
- hosts: "servers"
  environment:
    CONTAINER_NAME: "{{ lookup('env', 'CONTAINER_NAME') }}"
    IMAGE_TAG_NAME: "{{ lookup('env', 'IMAGE_TAG_NAME') }}"
    MCP_PORT: "{{ lookup('env', 'MCP_PORT') }}"
    PAYROLL_API_URL: "{{ lookup('env', 'PAYROLL_API_URL') }}"
    PAYROLL_ACCESS_TOKEN: "{{ lookup('env', 'PAYROLL_ACCESS_TOKEN') }}"

  tasks:
    - name: Log in
      community.docker.docker_login:
        username: "{{ lookup('env', 'DOCKER_REGISTRY_USERNAME') }}"
        password: "{{ lookup('env', 'DOCKER_REGISTRY_PASSWORD') }}"

    - name: Copy docker-compose.prod.yml
      copy:
        src: docker-compose.prod.yml
        dest: ~/docker-compose.prod.yml
        mode: 0644

    - name: DOWN the containers
      community.docker.docker_compose_v2:
        project_name: "{{ lookup('env', 'CONTAINER_NAME') }}"
        project_src: ~/
        state: absent
        files:
          - docker-compose.prod.yml

    - name: UP the containers
      community.docker.docker_compose_v2:
        project_name: "{{ lookup('env', 'CONTAINER_NAME') }}"
        project_src: ~/
        build: always
        recreate: always
        dependencies: no
        files:
          - docker-compose.prod.yml

    - name: Image Prune
      community.docker.docker_prune:
        images: yes

    - name: Delete docker-compose.prod.yml
      ansible.builtin.file:
        path: ~/docker-compose.prod.yml
        state: absent
