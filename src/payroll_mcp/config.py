"""
Configuration management for Payroll MCP server.

Handles environment variable loading, validation, and provides
a centralized configuration object for the application.
"""

import os
import sys
from dataclasses import dataclass
from typing import Optional
from urllib.parse import urljoin, urlparse

from dotenv import load_dotenv


class ConfigurationError(Exception):
    """Raised when configuration is invalid or missing required values."""
    pass


@dataclass(frozen=True)
class Config:
    """
    Application configuration loaded from environment variables.
    
    All configuration values are validated at initialization time.
    """
    
    # Required external API configuration
    payroll_api_url: str
    payroll_access_token: str
    
    # MCP server configuration
    mcp_host: str = "0.0.0.0"
    mcp_port: int = 8000
    
    # Logging configuration
    log_level: str = "INFO"
    log_format: str = "json"  # json or text
    
    # HTTP client configuration
    http_timeout: int = 30
    http_max_retries: int = 3
    
    def __post_init__(self) -> None:
        """Validate configuration after initialization."""
        self._validate_payroll_api_url()
        self._validate_payroll_access_token()
        self._validate_mcp_port()
        self._validate_log_level()
        self._validate_log_format()
        self._validate_http_timeout()
        self._validate_http_max_retries()
    
    def _validate_payroll_api_url(self) -> None:
        """Validate the payroll API URL."""
        if not self.payroll_api_url:
            raise ConfigurationError("PAYROLL_API_URL is required")
        
        try:
            parsed = urlparse(self.payroll_api_url)
            if not parsed.scheme or not parsed.netloc:
                raise ConfigurationError(
                    f"PAYROLL_API_URL must be a valid URL, got: {self.payroll_api_url}"
                )
            if parsed.scheme not in ("http", "https"):
                raise ConfigurationError(
                    f"PAYROLL_API_URL must use http or https scheme, got: {parsed.scheme}"
                )
        except Exception as e:
            raise ConfigurationError(
                f"Invalid PAYROLL_API_URL: {self.payroll_api_url}, error: {e}"
            ) from e
    
    def _validate_payroll_access_token(self) -> None:
        """Validate the payroll access token."""
        if not self.payroll_access_token:
            raise ConfigurationError("PAYROLL_ACCESS_TOKEN is required")
        
        if len(self.payroll_access_token.strip()) < 10:
            raise ConfigurationError(
                "PAYROLL_ACCESS_TOKEN appears to be too short (minimum 10 characters)"
            )
    
    def _validate_mcp_port(self) -> None:
        """Validate the MCP server port."""
        if not (1 <= self.mcp_port <= 65535):
            raise ConfigurationError(
                f"MCP_PORT must be between 1 and 65535, got: {self.mcp_port}"
            )
    
    def _validate_log_level(self) -> None:
        """Validate the log level."""
        valid_levels = {"DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"}
        if self.log_level.upper() not in valid_levels:
            raise ConfigurationError(
                f"LOG_LEVEL must be one of {valid_levels}, got: {self.log_level}"
            )
    
    def _validate_log_format(self) -> None:
        """Validate the log format."""
        valid_formats = {"json", "text"}
        if self.log_format.lower() not in valid_formats:
            raise ConfigurationError(
                f"LOG_FORMAT must be one of {valid_formats}, got: {self.log_format}"
            )
    
    def _validate_http_timeout(self) -> None:
        """Validate the HTTP timeout."""
        if self.http_timeout <= 0:
            raise ConfigurationError(
                f"HTTP_TIMEOUT must be positive, got: {self.http_timeout}"
            )
    
    def _validate_http_max_retries(self) -> None:
        """Validate the HTTP max retries."""
        if self.http_max_retries < 0:
            raise ConfigurationError(
                f"HTTP_MAX_RETRIES must be non-negative, got: {self.http_max_retries}"
            )
    
    @property
    def payroll_endpoint_url(self) -> str:
        """Get the full payroll endpoint URL."""
        return urljoin(self.payroll_api_url, "index.php?payroll-rpc=payroll-grid")
    
    @property
    def is_development(self) -> bool:
        """Check if running in development mode."""
        return self.log_level.upper() == "DEBUG"


def load_config(env_file: Optional[str] = None) -> Config:
    """
    Load configuration from environment variables.
    
    Args:
        env_file: Optional path to .env file to load
        
    Returns:
        Validated Config instance
        
    Raises:
        ConfigurationError: If configuration is invalid or missing required values
    """
    # Load .env file if specified or if .env exists
    if env_file:
        load_dotenv(env_file)
    elif os.path.exists(".env"):
        load_dotenv(".env")
    
    try:
        config = Config(
            payroll_api_url=os.getenv("PAYROLL_API_URL", "").strip(),
            payroll_access_token=os.getenv("PAYROLL_ACCESS_TOKEN", "").strip(),
            mcp_host=os.getenv("MCP_HOST", "0.0.0.0").strip(),
            mcp_port=int(os.getenv("MCP_PORT", "8000")),
            log_level=os.getenv("LOG_LEVEL", "INFO").strip().upper(),
            log_format=os.getenv("LOG_FORMAT", "json").strip().lower(),
            http_timeout=int(os.getenv("HTTP_TIMEOUT", "30")),
            http_max_retries=int(os.getenv("HTTP_MAX_RETRIES", "3")),
        )
        return config
    except ValueError as e:
        raise ConfigurationError(f"Invalid configuration value: {e}") from e
    except ConfigurationError:
        raise
    except Exception as e:
        raise ConfigurationError(f"Failed to load configuration: {e}") from e


def validate_environment() -> None:
    """
    Validate that all required environment variables are set.
    
    This function can be called at startup to fail fast if configuration is invalid.
    
    Raises:
        ConfigurationError: If configuration is invalid
        SystemExit: If configuration is invalid (exits with code 1)
    """
    try:
        load_config()
        print("✅ Configuration validation passed")
    except ConfigurationError as e:
        print(f"❌ Configuration error: {e}", file=sys.stderr)
        print("\nRequired environment variables:", file=sys.stderr)
        print("  - PAYROLL_API_URL: Base URL for the payroll API", file=sys.stderr)
        print("  - PAYROLL_ACCESS_TOKEN: Access token for API authentication", file=sys.stderr)
        print("\nOptional environment variables:", file=sys.stderr)
        print("  - MCP_HOST: Host to bind MCP server (default: 0.0.0.0)", file=sys.stderr)
        print("  - MCP_PORT: Port for MCP server (default: 8000)", file=sys.stderr)
        print("  - LOG_LEVEL: Logging level (default: INFO)", file=sys.stderr)
        print("  - LOG_FORMAT: Log format json|text (default: json)", file=sys.stderr)
        print("  - HTTP_TIMEOUT: HTTP request timeout in seconds (default: 30)", file=sys.stderr)
        print("  - HTTP_MAX_RETRIES: Max HTTP retries (default: 3)", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    # Allow running this module directly to validate configuration
    validate_environment()
