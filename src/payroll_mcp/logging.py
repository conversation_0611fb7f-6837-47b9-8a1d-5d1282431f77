"""
Structured logging configuration for Payroll MCP server.

This module sets up structured logging using structlog with support
for both JSON and text formats, proper log levels, and contextual
information for debugging and monitoring.
"""

import logging
import sys
from typing import Any, Dict

import structlog

from .config import Config


def setup_logging(config: Config) -> None:
    """
    Configure structured logging for the application.
    
    Args:
        config: Application configuration containing logging settings
    """
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, config.log_level.upper())
    )
    
    # Configure structlog processors
    processors = [
        # Add log level and timestamp
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
    ]
    
    # Add appropriate renderer based on format
    if config.log_format == "json":
        processors.append(structlog.processors.JSONRenderer())
    else:
        processors.extend([
            structlog.dev.ConsoleRenderer(colors=True),
        ])
    
    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        context_class=dict,
        cache_logger_on_first_use=True,
    )
    
    # Set up application-wide context
    structlog.contextvars.clear_contextvars()
    structlog.contextvars.bind_contextvars(
        service="payroll-mcp",
        version="0.1.0",
        log_format=config.log_format,
        log_level=config.log_level
    )
    
    # Log configuration
    logger = structlog.get_logger(__name__)
    logger.info(
        "Logging configured",
        log_level=config.log_level,
        log_format=config.log_format,
        is_development=config.is_development
    )


def get_logger(name: str, **context: Any) -> structlog.BoundLogger:
    """
    Get a logger with optional context.
    
    Args:
        name: Logger name (usually __name__)
        **context: Additional context to bind to the logger
        
    Returns:
        Configured structlog logger with context
    """
    logger = structlog.get_logger(name)
    if context:
        logger = logger.bind(**context)
    return logger


def log_request_context(
    tool_name: str,
    request_id: str,
    **additional_context: Any
) -> Dict[str, Any]:
    """
    Create standardized request context for logging.
    
    Args:
        tool_name: Name of the MCP tool being called
        request_id: Unique request identifier
        **additional_context: Additional context fields
        
    Returns:
        Dictionary of context fields for logging
    """
    context = {
        "tool_name": tool_name,
        "request_id": request_id,
        "component": "mcp_tool"
    }
    context.update(additional_context)
    return context


def log_api_context(
    api_endpoint: str,
    method: str,
    request_id: str,
    **additional_context: Any
) -> Dict[str, Any]:
    """
    Create standardized API context for logging.
    
    Args:
        api_endpoint: API endpoint being called
        method: HTTP method or RPC method
        request_id: Unique request identifier
        **additional_context: Additional context fields
        
    Returns:
        Dictionary of context fields for logging
    """
    context = {
        "api_endpoint": api_endpoint,
        "method": method,
        "request_id": request_id,
        "component": "api_client"
    }
    context.update(additional_context)
    return context


class LoggingMiddleware:
    """
    Middleware for logging MCP tool calls and responses.
    
    This can be used to wrap tool functions and automatically
    log their execution with proper context.
    """
    
    def __init__(self, tool_name: str):
        """
        Initialize logging middleware.
        
        Args:
            tool_name: Name of the tool being wrapped
        """
        self.tool_name = tool_name
        self.logger = get_logger(__name__, tool=tool_name)
    
    async def __call__(self, func, *args, **kwargs):
        """
        Wrap tool function with logging.
        
        Args:
            func: Tool function to wrap
            *args: Positional arguments
            **kwargs: Keyword arguments
            
        Returns:
            Tool function result
        """
        import time
        import uuid
        
        request_id = str(uuid.uuid4())[:8]
        start_time = time.time()
        
        # Log request start
        self.logger.info(
            "Tool execution started",
            **log_request_context(
                self.tool_name,
                request_id,
                args_count=len(args),
                kwargs_keys=list(kwargs.keys())
            )
        )
        
        try:
            # Execute the tool function
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # Log successful completion
            execution_time = time.time() - start_time
            self.logger.info(
                "Tool execution completed successfully",
                **log_request_context(
                    self.tool_name,
                    request_id,
                    execution_time=round(execution_time, 3),
                    result_type=type(result).__name__
                )
            )
            
            return result
            
        except Exception as e:
            # Log error
            execution_time = time.time() - start_time
            self.logger.error(
                "Tool execution failed",
                **log_request_context(
                    self.tool_name,
                    request_id,
                    execution_time=round(execution_time, 3),
                    error=str(e),
                    error_type=type(e).__name__
                )
            )
            raise


# Import asyncio at module level to avoid issues
import asyncio
