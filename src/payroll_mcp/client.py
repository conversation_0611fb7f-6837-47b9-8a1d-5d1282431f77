"""
JSON-RPC HTTP client for external payroll API integration.

This module provides an async HTTP client that handles JSON-RPC 2.0
communication with the external payroll grid API, including proper
authentication, error handling, and retry logic.
"""

import asyncio
import json
import uuid
from typing import Any, Dict, Optional

import httpx
import structlog

from .config import Config
from .models import (
    JsonRpcRequest,
    JsonRpcResponse,
    PayrollGridResponse,
    PayrollParameters,
)

logger = structlog.get_logger(__name__)


class PayrollApiError(Exception):
    """Base exception for payroll API errors."""

    pass


class PayrollApiConnectionError(PayrollApiError):
    """Raised when connection to the payroll API fails."""

    pass


class PayrollApiTimeoutError(PayrollApiError):
    """Raised when payroll API request times out."""

    pass


class PayrollApiAuthenticationError(PayrollApiError):
    """Raised when authentication with payroll API fails."""

    pass


class PayrollApiValidationError(PayrollApiError):
    """Raised when payroll API returns validation errors."""

    pass


class PayrollApiServerError(PayrollApiError):
    """Raised when payroll API returns server errors."""

    pass


class PayrollApiClient:
    """
    Async HTTP client for payroll grid API using JSON-RPC 2.0.

    This client handles authentication, request formatting, response parsing,
    error handling, and retry logic for the external payroll API.
    """

    def __init__(self, config: Config):
        """
        Initialize the payroll API client.

        Args:
            config: Application configuration containing API URL and token
        """
        self.config = config
        self._client: Optional[httpx.AsyncClient] = None
        self._request_id_counter = 0

        # Setup logging context
        self.logger = logger.bind(
            component="payroll_api_client", api_url=config.payroll_endpoint_url
        )

    async def __aenter__(self) -> "PayrollApiClient":
        """Async context manager entry."""
        await self._ensure_client()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Async context manager exit."""
        await self.close()

    async def _ensure_client(self) -> None:
        """Ensure HTTP client is initialized."""
        if self._client is None:
            timeout = httpx.Timeout(
                connect=10.0, read=self.config.http_timeout, write=10.0, pool=5.0
            )

            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "Authorization": f"Bearer {self.config.payroll_access_token}",
                "User-Agent": "PayrollMCP/0.1.0",
            }

            self._client = httpx.AsyncClient(
                timeout=timeout,
                headers=headers,
                follow_redirects=True,
                limits=httpx.Limits(max_keepalive_connections=5, max_connections=10),
            )

            self.logger.info("HTTP client initialized")

    async def close(self) -> None:
        """Close the HTTP client."""
        if self._client:
            await self._client.aclose()
            self._client = None
            self.logger.info("HTTP client closed")

    def _generate_request_id(self) -> str:
        """Generate a unique request ID."""
        self._request_id_counter += 1
        return f"{uuid.uuid4().hex[:8]}-{self._request_id_counter}"

    async def read_payroll_grid(
        self,
        parameters: PayrollParameters,
        page: int = 1,
        rows: int = 30,
        sort: str = "owner_names",
        order: str = "asc",
    ) -> PayrollGridResponse:
        """
        Call the payroll grid API with the specified parameters.

        Args:
            parameters: Validated payroll parameters
            page: Page number for pagination (default: 1)
            rows: Number of rows per page (default: 30)
            sort: Sort field (default: "owner_names")
            order: Sort order "asc" or "desc" (default: "asc")

        Returns:
            PayrollGridResponse containing the API response data

        Raises:
            PayrollApiError: For various API-related errors
        """
        request_id = self._generate_request_id()

        # Create JSON-RPC request in the exact format specified
        rpc_request = JsonRpcRequest(
            method="read",
            params=[parameters.to_api_params(), page, rows, sort, order],
            id=request_id,
        )

        self.logger.info(
            "Making payroll API request",
            request_id=request_id,
            method=rpc_request.method,
            page=page,
            rows=rows,
            sort=sort,
            order=order,
            param_count=len(parameters.to_api_params()),
        )

        try:
            response = await self._make_request_with_retry(rpc_request)
            return self._parse_response(response, request_id)

        except Exception as e:
            self.logger.error(
                "Payroll API request failed",
                request_id=request_id,
                error=str(e),
                error_type=type(e).__name__,
            )
            raise

    async def _make_request_with_retry(
        self, rpc_request: JsonRpcRequest
    ) -> JsonRpcResponse:
        """
        Make HTTP request with retry logic.

        Args:
            rpc_request: JSON-RPC request to send

        Returns:
            Parsed JSON-RPC response

        Raises:
            PayrollApiError: For various request failures
        """
        await self._ensure_client()

        last_exception = None

        for attempt in range(self.config.http_max_retries + 1):
            try:
                self.logger.debug(
                    "Attempting HTTP request",
                    attempt=attempt + 1,
                    max_attempts=self.config.http_max_retries + 1,
                    request_id=rpc_request.id,
                )

                response = await self._client.post(
                    self.config.payroll_endpoint_url, json=rpc_request.model_dump()
                )

                # Handle HTTP status codes
                if response.status_code == 401:
                    raise PayrollApiAuthenticationError(
                        "Authentication failed - check PAYROLL_ACCESS_TOKEN"
                    )
                elif response.status_code == 403:
                    raise PayrollApiAuthenticationError(
                        "Access forbidden - insufficient permissions"
                    )
                elif response.status_code >= 500:
                    raise PayrollApiServerError(
                        f"Server error: HTTP {response.status_code}"
                    )
                elif response.status_code >= 400:
                    raise PayrollApiValidationError(
                        f"Client error: HTTP {response.status_code} - {response.text}"
                    )

                response.raise_for_status()

                # Parse JSON-RPC response
                try:
                    response_data = response.json()
                    return JsonRpcResponse.model_validate(response_data)
                except json.JSONDecodeError as e:
                    raise PayrollApiError(f"Invalid JSON response from API: {e}") from e
                except Exception as e:
                    raise PayrollApiError(f"Failed to parse API response: {e}") from e

            except httpx.TimeoutException as e:
                last_exception = PayrollApiTimeoutError(
                    f"Request timeout after {self.config.http_timeout}s"
                )
                self.logger.warning(
                    "Request timeout, will retry",
                    attempt=attempt + 1,
                    timeout=self.config.http_timeout,
                    request_id=rpc_request.id,
                )

            except httpx.ConnectError as e:
                last_exception = PayrollApiConnectionError(f"Connection failed: {e}")
                self.logger.warning(
                    "Connection failed, will retry",
                    attempt=attempt + 1,
                    error=str(e),
                    request_id=rpc_request.id,
                )

            except (PayrollApiAuthenticationError, PayrollApiValidationError):
                # Don't retry authentication or validation errors
                raise

            except Exception as e:
                last_exception = e
                self.logger.warning(
                    "Request failed, will retry",
                    attempt=attempt + 1,
                    error=str(e),
                    error_type=type(e).__name__,
                    request_id=rpc_request.id,
                )

            # Wait before retry (exponential backoff)
            if attempt < self.config.http_max_retries:
                wait_time = min(2**attempt, 10)  # Max 10 seconds
                self.logger.debug(
                    "Waiting before retry",
                    wait_time=wait_time,
                    request_id=rpc_request.id,
                )
                await asyncio.sleep(wait_time)

        # All retries exhausted
        if last_exception:
            raise last_exception
        else:
            raise PayrollApiError("All retry attempts failed")

    def _parse_response(
        self, response: JsonRpcResponse, request_id: str
    ) -> PayrollGridResponse:
        """
        Parse and validate the JSON-RPC response.

        Args:
            response: JSON-RPC response from API
            request_id: Original request ID for logging

        Returns:
            Validated PayrollGridResponse

        Raises:
            PayrollApiError: If response contains errors or is invalid
        """
        # Check for JSON-RPC errors
        if response.error:
            error_code = response.error.get("code", "unknown")
            error_message = response.error.get("message", "Unknown error")

            self.logger.error(
                "JSON-RPC error response",
                request_id=request_id,
                error_code=error_code,
                error_message=error_message,
            )

            raise PayrollApiError(f"API returned error {error_code}: {error_message}")

        # Validate response structure
        if not response.result:
            raise PayrollApiError("API response missing result data")

        try:
            payroll_response = PayrollGridResponse.model_validate(response.result)

            self.logger.info(
                "Successfully parsed API response",
                request_id=request_id,
                total_rows=payroll_response.total,
                returned_rows=len(payroll_response.rows),
                footer_items=len(payroll_response.footer),
            )

            return payroll_response

        except Exception as e:
            self.logger.error(
                "Failed to parse payroll response",
                request_id=request_id,
                error=str(e),
                response_keys=list(response.result.keys()) if response.result else [],
            )
            raise PayrollApiError(f"Invalid payroll response structure: {e}") from e
