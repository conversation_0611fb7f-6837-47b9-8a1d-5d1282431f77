# Payroll MCP Python - Environment Configuration Example
# Copy this file to .env and fill in your actual values

# =============================================================================
# REQUIRED CONFIGURATION
# =============================================================================

# Payroll API Configuration (REQUIRED)
# Base URL for the external payroll API (without the endpoint path)
# Example: https://api.payroll-system.com or http://localhost:3000
PAYROLL_API_URL=https://your-payroll-api.example.com

# Access token for authenticating with the payroll API (REQUIRED)
# This token will be sent as "Authorization: Bearer <token>" header
PAYROLL_ACCESS_TOKEN=your-secret-access-token-here

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# MCP Server Configuration
# Host to bind the MCP server (default: 0.0.0.0 for all interfaces)
MCP_HOST=0.0.0.0

# Port for the MCP server to listen on (default: 8000)
MCP_PORT=8000

# Logging Configuration
# Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL (default: INFO)
LOG_LEVEL=INFO

# Log format: json (for production) or text (for development) (default: json)
LOG_FORMAT=json

# HTTP Client Configuration
# Timeout for HTTP requests to the payroll API in seconds (default: 30)
HTTP_TIMEOUT=30

# Maximum number of retry attempts for failed HTTP requests (default: 3)
HTTP_MAX_RETRIES=3

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Uncomment these for development/debugging
# LOG_LEVEL=DEBUG
# LOG_FORMAT=text

# =============================================================================
# PRODUCTION NOTES
# =============================================================================

# For production deployment:
# 1. Use strong, unique access tokens
# 2. Use HTTPS URLs for the payroll API
# 3. Set LOG_FORMAT=json for structured logging
# 4. Consider setting LOG_LEVEL=WARNING or ERROR to reduce log volume
# 5. Adjust HTTP_TIMEOUT based on your API's response times
# 6. Monitor and adjust HTTP_MAX_RETRIES based on network reliability

# Security considerations:
# - Keep this file secure and never commit it to version control
# - Rotate access tokens regularly
# - Use environment-specific configurations
# - Consider using secrets management systems in production
