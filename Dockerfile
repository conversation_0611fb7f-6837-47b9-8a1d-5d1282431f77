# Multi-stage Dockerfile for Payroll MCP Python server
# Optimized for production with security best practices

# Build stage - Install dependencies and build the application
FROM python:3.13-slim AS builder

# Set environment variables for build
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies needed for building
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install uv for fast Python package management
RUN pip install uv

# Create application directory
WORKDIR /app

# Copy dependency files and README (needed for package build)
COPY pyproject.toml README.md ./

# Copy source code (needed for editable install)
COPY src/ ./src/

# Install Python dependencies using uv
RUN uv pip install --system --no-cache-dir -e .

# Runtime stage - Minimal image for production
FROM python:3.13-slim AS runtime

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/home/<USER>/.local/bin:$PATH" \
    PYTHONPATH="/app/src"

# Install runtime system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for security
RUN groupadd --gid 1000 appuser \
    && useradd --uid 1000 --gid appuser --shell /bin/bash --create-home appuser

# Create application directory and set ownership
WORKDIR /app
RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Copy Python environment from builder stage
COPY --from=builder --chown=appuser:appuser /usr/local/lib/python3.13/site-packages /usr/local/lib/python3.13/site-packages
COPY --from=builder --chown=appuser:appuser /usr/local/bin /usr/local/bin

# Copy application code
COPY --from=builder --chown=appuser:appuser /app/src ./src

# Create directories for logs and data
RUN mkdir -p /app/logs /app/data

# Expose the default MCP port
EXPOSE 8000

# Health check to ensure the server is running
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${MCP_PORT:-8000}/sse || exit 1

# Default command to run the server
CMD ["python", "-m", "payroll_mcp.server"]

# Labels for metadata
LABEL maintainer="Payroll MCP Team <<EMAIL>>" \
      version="0.1.0" \
      description="Model Context Protocol server for Payroll Grid API integration" \
      org.opencontainers.image.title="Payroll MCP Python" \
      org.opencontainers.image.description="FastMCP server for payroll grid API integration" \
      org.opencontainers.image.version="0.1.0" \
      org.opencontainers.image.vendor="Payroll MCP Team" \
      org.opencontainers.image.licenses="MIT"
