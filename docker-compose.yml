services:
  payroll-mcp:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    image: payroll-mcp-python:latest
    container_name: payroll-mcp-server
    
    # Environment configuration
    env_file:
      - .env
    environment:
      # Override defaults if needed
      - MCP_HOST=0.0.0.0
      - MCP_PORT=8000
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
      - HTTP_TIMEOUT=30
      - HTTP_MAX_RETRIES=3
    
    # Port mapping
    ports:
      - "${MCP_PORT:-8000}:8000"
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/sse"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    
    # Restart policy
    restart: unless-stopped
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 128M
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    # Security options
    security_opt:
      - no-new-privileges:true
    
    # Read-only root filesystem for security
    read_only: true
    
    # Temporary filesystems for writable directories
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
      - /app/logs:noexec,nosuid,size=50m
    
    # Networks
    networks:
      - payroll-mcp-network

  # Development override service (use with docker-compose.override.yml)
  payroll-mcp-dev:
    extends:
      service: payroll-mcp
    build:
      target: builder
    environment:
      - LOG_LEVEL=DEBUG
      - LOG_FORMAT=text
    volumes:
      # Mount source code for development
      - ./src:/app/src:ro
      - ./pyproject.toml:/app/pyproject.toml:ro
    # Remove read-only restriction for development
    read_only: false
    profiles:
      - dev

networks:
  payroll-mcp-network:

# Volumes for persistent data (if needed in the future)
volumes:
  payroll-mcp-logs:
    driver: local
    name: payroll-mcp-logs
