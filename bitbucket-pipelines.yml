image: atlassian/default-image:4

options:
  docker: true

definitions:
  steps:
    - step: &build
        name: "Build"
        runs-on:
          - "self.hosted"
          - "linux"
          - "gs"
          - "build"
        services:
          - docker
        script:
          - echo "Building the image and push to docker hub registry"
          - export PATH=/usr/bin:$PATH
          - export DOCKER_BUILDKIT=0
          - docker login -u $DOCKERHUB_USER -p $DOCKERHUB_PASSWORD
          - docker-compose -f docker-compose.cd.yml build --no-cache --force-rm --compress
          - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
          - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
          - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
          - docker tag ${BITBUCKET_REPO_SLUG}:${BITBUCKET_COMMIT} ${DOCKERHUB_REPO_NAME}:${IMAGE_TAG_NAME}
          - docker push ${DOCKERHUB_REPO_NAME}:${IMAGE_TAG_NAME}
          - docker logout
          - echo ${DOCKERHUB_REPO_NAME}:${IMAGE_TAG_NAME} "is pushed to Docker Hub"
    - step: &deploy
        name: "Deploy on Staging"
        runs-on:
          - self.hosted
          - gs
          - linux
        image: willhallonline/ansible:2.18-alpine-3.20
        services:
          - docker
        script:
          - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
          - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
          - export ANSIBLE_HOST_KEY_CHECKING="False"
          - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
          - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
          - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
          - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
          - export CONTAINER_NAME=$CONTAINER_NAME
          - export MCP_PORT=$MCP_PORT
          - export PAYROLL_API_URL=$PAYROLL_API_URL
          - export PAYROLL_ACCESS_TOKEN=$PAYROLL_ACCESS_TOKEN
          - ansible-galaxy collection install community.docker
          - ansible-playbook -i .ansible/hosts ansible-deploy.yml -l staging

pipelines:
  custom:
    "Staging":
      - step: *build
      - step:
          <<: *deploy
          deployment: staging