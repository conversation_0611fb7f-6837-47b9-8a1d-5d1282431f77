# Payroll Grid API Parameters Documentation

## Overview

The `readPayrollGrid()` method in `PayrollGrid.php` processes JSON payloads to generate payroll data for owners, heritors, and their associated contracts. This document provides comprehensive documentation for all parameters in the JSON request payload.

## API Method Signature

```php
public function readPayrollGrid(array $data, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
```

## Core Parameters

### `type` (Required)
- **Data Type**: `string`
- **Purpose**: Determines the operation mode and type of data returned
- **Expected Values**: 
  - `"owners"` - Returns paginated owner payroll data
  - `"sums"` - Returns aggregated summary data
  - `"payroll_by_owner"` - Returns detailed payroll for a specific owner
- **Required**: Yes
- **Default Behavior**: Returns empty result if not provided or invalid
- **Usage**: Controls the main query logic and data structure returned

### `farming_year` (Required)
- **Data Type**: `integer`
- **Purpose**: Specifies the farming year for payroll calculations
- **Expected Values**: Valid farming year ID (e.g., `16`)
- **Required**: Yes
- **Default Behavior**: Method fails if not provided
- **Usage**: Used to determine date ranges and filter contracts/payments by farming period

## Date Range Parameters

### `payroll_from_date`
- **Data Type**: `string` (ISO date format)
- **Purpose**: Start date for payroll period filtering
- **Expected Values**: Date in format `"YYYY-MM-DD"` (e.g., `"2024-10-01"`)
- **Required**: Yes for date-based filtering
- **Default Behavior**: Used in conjunction with `payroll_to_date` to determine farming years
- **Usage**: Filters contracts, annexes, and payments within the specified date range
- **Relationships**: Works with `payroll_to_date` to calculate farming year ranges via `getFarmings()` method

### `payroll_to_date`
- **Data Type**: `string` (ISO date format)
- **Purpose**: End date for payroll period filtering
- **Expected Values**: Date in format `"YYYY-MM-DD"` (e.g., `"2025-09-30"`)
- **Required**: Yes for date-based filtering
- **Default Behavior**: Used with `payroll_from_date` to determine valid contract periods
- **Usage**: Filters active contracts and determines owner death status within period
- **Relationships**: Must be later than `payroll_from_date`

## Location and Administrative Filters

### `payroll_ekate`
- **Data Type**: `array` of strings
- **Purpose**: Filters results by EKATE (administrative territorial codes)
- **Expected Values**: Array of EKATE codes or `[""]` for all
- **Required**: No
- **Default Behavior**: If empty array with empty string `[""]`, uses `=` comparison; otherwise uses `IN` comparison
- **Usage**: Filters plots by administrative location
- **Special Processing**: 
  - Empty arrays are removed from filters
  - Single empty string element changes comparison type
  - Processed by `filterPayrollEkateValue()` and `getPayrollEkateCompareType()`

### `payroll_farming`
- **Data Type**: `array` of strings/integers
- **Purpose**: Filters by specific farming operations/entities
- **Expected Values**: Array of farming IDs or `[""]` for all user farmings
- **Required**: No
- **Default Behavior**: If empty, includes all user-allowed farming IDs
- **Usage**: Restricts results to specific farming operations
- **Security**: Automatically intersected with user's allowed farming IDs
- **Processing**: Empty strings are filtered out via `arrayHelper->filterEmptyStringArr()`

## Owner Type and Identification Filters

### `owner_type`
- **Data Type**: `string`
- **Purpose**: Filters by owner type (individual vs company)
- **Expected Values**: 
  - `"0"` - Companies
  - `"1"` - Individuals  
  - `"0,1"` - Both types (comma-separated)
- **Required**: No
- **Default Behavior**: Includes both types (`[0, 1]`) if not specified
- **Usage**: Determines whether to search individuals, companies, or both
- **Special Processing**: 
  - Single values use `=` comparison
  - Comma-separated values use `IN` comparison
  - Automatically parsed and converted to appropriate array format

### `owner_names`
- **Data Type**: `string`
- **Purpose**: Text search filter for owner names (individuals only)
- **Expected Values**: Partial or full name (e.g., `"кадир"`)
- **Required**: No
- **Default Behavior**: No name filtering if empty
- **Usage**: Searches concatenated first name, surname, and last name
- **Processing**: 
  - Converts to lowercase for case-insensitive search
  - Replaces multiple spaces with regex pattern `.*`
  - Uses PostgreSQL regex matching (`~` operator)
  - Only applies to individual owners (owner_type = 1)

### `egn`
- **Data Type**: `string`
- **Purpose**: Exact or partial match for individual identification number
- **Expected Values**: EGN number (Bulgarian personal ID)
- **Required**: No
- **Default Behavior**: No EGN filtering if empty
- **Usage**: Filters individual owners by identification number
- **Processing**: Uses `ILIKE` for partial matching
- **Relationships**: Overridden by `owner_egns` array if provided

### `eik`
- **Data Type**: `string`
- **Purpose**: Exact or partial match for company identification number
- **Expected Values**: EIK number (Bulgarian company ID)
- **Required**: No
- **Default Behavior**: No EIK filtering if empty
- **Usage**: Filters company owners by identification number
- **Processing**: Uses `ILIKE` for partial matching
- **Relationships**: Overridden by `company_eiks` array if provided

### `company_name`
- **Data Type**: `string`
- **Purpose**: Text search filter for company names
- **Expected Values**: Partial or full company name
- **Required**: No
- **Default Behavior**: No company name filtering if empty
- **Usage**: Searches company names with partial matching
- **Processing**: Uses `ILIKE` for case-insensitive partial matching

## Advanced Owner Filters

### `owner_egns`
- **Data Type**: `array` of strings
- **Purpose**: Exact matching for multiple individual identification numbers
- **Expected Values**: Array of EGN numbers (e.g., `["1234567890", "0987654321"]`)
- **Required**: No
- **Default Behavior**: No filtering if empty
- **Usage**: Filters for specific individuals by exact EGN match
- **Processing**: 
  - Uses `IN` comparison for exact matching
  - Overrides `egn` parameter when provided
  - Automatically sets `is_heritor = false` filter
- **Relationships**: Takes precedence over `egn` parameter

### `company_eiks`
- **Data Type**: `array` of strings
- **Purpose**: Exact matching for multiple company identification numbers
- **Expected Values**: Array of EIK numbers
- **Required**: No
- **Default Behavior**: No filtering if empty
- **Usage**: Filters for specific companies by exact EIK match
- **Processing**: 
  - Uses `IN` comparison for exact matching
  - Overrides `eik` parameter when provided
- **Relationships**: Takes precedence over `eik` parameter

## Representative Filters

### `rep_names`
- **Data Type**: `string`
- **Purpose**: Text search filter for representative names
- **Expected Values**: Partial or full representative name
- **Required**: No
- **Default Behavior**: No representative name filtering if empty
- **Usage**: Searches concatenated representative first name, surname, and last name
- **Processing**: 
  - Converts to lowercase for case-insensitive search
  - Replaces multiple spaces with regex pattern `.*`
  - Uses PostgreSQL regex matching (`~` operator)

### `rep_egn`
- **Data Type**: `string`
- **Purpose**: Filter for representative identification number
- **Expected Values**: Representative's EGN number
- **Required**: No
- **Default Behavior**: No representative EGN filtering if empty
- **Usage**: Filters by representative's identification number
- **Processing**: Uses `ILIKE` for partial matching

### `rep_rent_place`
- **Data Type**: `string`
- **Purpose**: Filter for representative's rent place/location
- **Expected Values**: Location identifier or name
- **Required**: No
- **Default Behavior**: No filtering if empty
- **Usage**: Filters by representative's registered rent location
- **Processing**: Applied as direct filter in query options

## Location Filters

### `rent_place`
- **Data Type**: `string`
- **Purpose**: Filter for owner's rent place/location
- **Expected Values**: Location identifier or name
- **Required**: No
- **Default Behavior**: No filtering if empty
- **Usage**: Filters by owner's registered rent location
- **Processing**: Applied as direct filter in query options
- **Relationships**: Can work in conjunction with `rep_rent_place`

## Heritor Filters

### `heritor_names`
- **Data Type**: `string`
- **Purpose**: Text search filter for heritor names
- **Expected Values**: Partial or full heritor name
- **Required**: No
- **Default Behavior**: No heritor filtering if empty
- **Usage**: Searches for ancestors of heritors matching the name pattern
- **Processing**: 
  - Triggers `_getHeritorAncestors()` method
  - Uses regex pattern matching similar to owner names
  - Returns empty result if no matching ancestors found
- **Relationships**: Works with `heritor_egn` for combined heritor filtering

### `heritor_egn`
- **Data Type**: `string`
- **Purpose**: Filter for heritor identification number
- **Expected Values**: Heritor's EGN number
- **Required**: No
- **Default Behavior**: No heritor filtering if empty
- **Usage**: Searches for ancestors of heritors with matching EGN
- **Processing**: 
  - Triggers `_getHeritorAncestors()` method
  - Uses exact EGN matching
  - Returns empty result if no matching ancestors found
- **Relationships**: Works with `heritor_names` for combined heritor filtering

## Parameter Relationships and Processing Rules

### Filtering Hierarchy
1. **Array parameters override string parameters**: `owner_egns` overrides `egn`, `company_eiks` overrides `eik`
2. **Empty array handling**: Arrays with single empty string element are treated specially
3. **User permission filtering**: `payroll_farming` is automatically restricted to user's allowed farmings
4. **Heritor filtering**: When heritor filters are used, only ancestor owners are included

### Special Processing Notes
1. **Case sensitivity**: Text searches are case-insensitive
2. **Regex patterns**: Name searches support space-separated terms with wildcard matching
3. **Date validation**: Date parameters must be valid ISO format dates
4. **Security**: All farming-related filters are intersected with user permissions
5. **Performance**: Empty filters are removed to optimize database queries
